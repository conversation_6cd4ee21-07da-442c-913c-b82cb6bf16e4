import { useState, useEffect, useCallback } from 'react';
import { Message, ChatSession, FileAttachment } from '@/types';

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<FileAttachment[]>([]);

  useEffect(() => {
    // Load chat history from localStorage
    loadChatHistory();
  }, []);

  const loadChatHistory = () => {
    try {
      const savedSessions = localStorage.getItem('chatSessions');
      if (savedSessions) {
        const sessions = JSON.parse(savedSessions);
        setChatSessions(sessions);
        
        // Load the most recent session if available
        if (sessions.length > 0) {
          const mostRecent = sessions[0];
          setCurrentSessionId(mostRecent.id);
          setMessages(mostRecent.messages);
        }
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  const saveChatHistory = useCallback((sessions: ChatSession[]) => {
    try {
      localStorage.setItem('chatSessions', JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  }, []);

  const createNewSession = () => {
    // Prevent multiple untitled/empty chats
    const existingEmpty = chatSessions.find(
      (s) => s.title === 'New Chat' && (!s.messages || s.messages.length === 0)
    );
    if (existingEmpty) {
      setCurrentSessionId(existingEmpty.id);
      setMessages(existingEmpty.messages || []);
      setAttachedFiles([]);
      return;
    }
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const updatedSessions = [newSession, ...chatSessions];
    setChatSessions(updatedSessions);
    setCurrentSessionId(newSession.id);
    setMessages([]);
    setAttachedFiles([]);
    saveChatHistory(updatedSessions);
  };

  const sendMessage = async (content: string, files?: FileAttachment[]) => {
    if (!content.trim() && (!files || files.length === 0)) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date(),
      files: files || attachedFiles,
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setAttachedFiles([]);
    setIsLoading(true);

    try {
      // Simulate API call - replace with actual chat API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `I received your message: "${content}". This is a simulated response from the ZiaHR chatbot.`,
        isUser: false,
        timestamp: new Date(),
      };

      const finalMessages = [...updatedMessages, botMessage];
      setMessages(finalMessages);

      // Update current session
      if (currentSessionId) {
        const updatedSessions = chatSessions.map(session => 
          session.id === currentSessionId 
            ? { 
                ...session, 
                messages: finalMessages, 
                updatedAt: new Date(),
                title: finalMessages.length === 2 ? content.slice(0, 50) + '...' : session.title
              }
            : session
        );
        setChatSessions(updatedSessions);
        saveChatHistory(updatedSessions);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSession = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
      setAttachedFiles([]);
    }
  };

  const deleteSession = (sessionId: string) => {
    const updatedSessions = chatSessions.filter(s => s.id !== sessionId);
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);

    if (currentSessionId === sessionId) {
      if (updatedSessions.length > 0) {
        loadSession(updatedSessions[0].id);
      } else {
        createNewSession();
      }
    }
  };

  const archiveSession = (sessionId: string) => {
    const updatedSessions = chatSessions.map(session =>
      session.id === sessionId ? { ...session, isArchived: true } : session
    );
    setChatSessions(updatedSessions);
    saveChatHistory(updatedSessions);
  };

  const addFileAttachment = (file: FileAttachment) => {
    setAttachedFiles(prev => [...prev, file]);
  };

  const removeFileAttachment = (fileId: string) => {
    setAttachedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAllChats = () => {
    setChatSessions([]);
    setMessages([]);
    setCurrentSessionId(null);
    setAttachedFiles([]);
    localStorage.removeItem('chatSessions');
    createNewSession();
  };

  return {
    messages,
    chatSessions: chatSessions.filter(s => !s.isArchived),
    archivedSessions: chatSessions.filter(s => s.isArchived),
    currentSessionId,
    isLoading,
    attachedFiles,
    sendMessage,
    createNewSession,
    loadSession,
    deleteSession,
    archiveSession,
    addFileAttachment,
    removeFileAttachment,
    clearAllChats,
  };
};
