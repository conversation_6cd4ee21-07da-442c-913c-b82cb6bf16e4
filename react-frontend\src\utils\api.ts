// API utility functions for the ZiaHR chatbot

export const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5051";

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // Ensure cookies/session are sent for all requests
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      let errorData;
      try {
        errorData = await response.json();
        errorMessage = errorData.message || errorMessage;

        // Special handling for 2FA case - return the response data instead of throwing
        if (response.status === 401 && errorData && errorData.message === "2FA code required") {
          console.log('2FA case detected, returning response data:', errorData);
          return errorData; // Return the full response data
        }
      } catch (e) {
        // If we can't parse the error response, use the default message
      }

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    // Check if it's a network error
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error. Please check if the server is running.');
    }
    throw error;
  }
}

// Authentication API calls
export const authAPI = {
  login: async (email: string, password: string, twoFACode?: string) => {
    return apiRequest('/api/login', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        two_fa_code: twoFACode
      }),
    });
  },

  register: async (fullName: string, email: string, password: string, employeeId?: string) => {
    return apiRequest('/api/register', {
      method: 'POST',
      body: JSON.stringify({
        full_name: fullName,
        email,
        password,
        employee_id: employeeId
      }),
    });
  },

  logout: async () => {
    return apiRequest('/api/logout', {
      method: 'POST',
    });
  },

  verify2FA: async (code: string) => {
    return apiRequest('/api/auth/verify-2fa', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  },

  get2FASetup: async (email: string) => {
    return apiRequest('/api/user-2fa-setup', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },
};

// Chat API calls
export const chatAPI = {
  sendMessage: async (message: string, files?: File[]) => {
    const formData = new FormData();
    formData.append('message', message);
    
    if (files) {
      files.forEach((file, index) => {
        formData.append(`file_${index}`, file);
      });
    }

    return apiRequest('/api/chat/send', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },

  getChatHistory: async () => {
    return apiRequest('/api/chat/history');
  },

  deleteChat: async (chatId: string) => {
    return apiRequest(`/api/chat/${chatId}`, {
      method: 'DELETE',
    });
  },
};

// File API calls
export const fileAPI = {
  upload: async (files: File[]) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`file_${index}`, file);
    });

    return apiRequest('/api/files/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },

  preview: async (fileId: string) => {
    return apiRequest(`/api/files/preview/${fileId}`);
  },
};

// HR Escalation API calls
export const escalationAPI = {
  submit: async (escalationData: any) => {
    return apiRequest('/api/escalation/submit', {
      method: 'POST',
      body: JSON.stringify(escalationData),
    });
  },

  getHRPersons: async () => {
    return apiRequest('/api/escalation/hr-persons');
  },
};

// User API calls
export const userAPI = {
  updateProfile: async (userData: any) => {
    return apiRequest('/api/user/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  getProfile: async () => {
    return apiRequest('/api/user/profile');
  },
};
