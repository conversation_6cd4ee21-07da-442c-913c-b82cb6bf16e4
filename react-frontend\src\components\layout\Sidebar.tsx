import React, { useState } from 'react';
import { ChatSession } from '@/types';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  chatSessions: ChatSession[];
  currentSessionId: string | null;
  onNewChat: () => void;
  onLoadSession: (sessionId: string) => void;
  onDeleteSession: (sessionId: string) => void;
  onArchiveSession: (sessionId: string) => void;
  onOpenSearch: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggle,
  chatSessions,
  currentSessionId,
  onNewChat,
  onLoadSession,
  onDeleteSession,
  onArchiveSession,
  onOpenSearch,
}) => {
  const [hoveredSession, setHoveredSession] = useState<string | null>(null);

  const formatDate = (date: string | Date) => {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return 'Today';
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return d.toLocaleDateString();
    }
  };

  const truncateTitle = (title: string, maxLength: number = 30) => {
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  };

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''} bg-background text-foreground`} id="sidebar">
      {/* Sidebar header with toggle and app icon */}
      <div className="sidebar-header flex items-center gap-2 px-2 min-h-[32px]">
        <div className="sidebar-brand-logo flex items-center cursor-pointer mx-1" id="appIconBtn" tabIndex={0} title="Home" aria-label="ZiaHR Home">
          <img src="/img/favicon.png" alt="ZiaHR logo" className="sidebar-app-icon w-6 h-6 rounded" />
        </div>
        <button id="toggleSidebar" className="sidebar-toggle ml-auto p-1 rounded hover:bg-accent focus:outline-none" onClick={onToggle} aria-label="Toggle sidebar">
          <i className="fas fa-bars"></i>
        </button>
      </div>

      {/* Sidebar menu items */}
      <nav className="sidebar-nav">
        <div className="sidebar-menu">
          <div className="sidebar-menu-item flex items-center px-4 py-2 cursor-pointer hover:bg-accent transition-colors" id="sidebarNewChatBtn" onClick={onNewChat}>
            <img src="/img/new-chat-icon-larger.svg" alt="New Chat" className="sidebar-menu-icon w-5 h-5 mr-2" />
            <span className="sidebar-menu-text text-sm font-medium">New Chat</span>
          </div>
          <div className="sidebar-menu-item" id="sidebarSearchBtn" onClick={onOpenSearch}>
            <i className="fas fa-search sidebar-menu-icon"></i>
            <span className="sidebar-menu-text">Search chats</span>
          </div>
          <div className="sidebar-menu-item">
            <i className="fas fa-users sidebar-menu-icon"></i>
            <span className="sidebar-menu-text">HR Team</span>
          </div>
        </div>

        {/* Chat history section */}
        <div className="sidebar-conversations">
          <div className="sidebar-section-header">
            <span className="sidebar-section-title">Chats</span>
          </div>
          <div className="chat-history-list" id="chatHistory">
            {chatSessions.map((session) => (
              <div
                key={session.id}
                className={`chat-history-item ${currentSessionId === session.id ? 'active' : ''}`}
                onClick={() => onLoadSession(session.id)}
                onMouseEnter={() => setHoveredSession(session.id)}
                onMouseLeave={() => setHoveredSession(null)}
              >
                <div className="chat-title">{truncateTitle(session.title)}</div>
                <div className="chat-date">{formatDate(session.updatedAt)}</div>
                {hoveredSession === session.id && (
                  <div className="chat-actions">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onArchiveSession(session.id);
                      }}
                      title="Archive"
                    >
                      <i className="fas fa-archive"></i>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteSession(session.id);
                      }}
                      title="Delete"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Sidebar bottom */}
        <div className="sidebar-bottom">
          <form id="uploadForm" className="upload-form">
            <input type="file" id="fileUpload" accept=".pdf,.docx,.txt,.md" hidden multiple />
          </form>
          <div id="uploadStatus" className="upload-status" style={{ display: 'none' }}></div>
        </div>
      </nav>
    </div>

  );
};

export default Sidebar;
