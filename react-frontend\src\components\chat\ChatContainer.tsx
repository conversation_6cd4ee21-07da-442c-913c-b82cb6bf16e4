import React from 'react';
import { Message, FileAttachment } from '@/types';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import { cn } from '@/lib/utils';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  className?: string;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  className,
}) => {

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {messages.length === 0 ? (
        <div className="flex-1 flex flex-col justify-center items-center h-full">
          <ChatInput
            onSendMessage={onSendMessage}
            attachedFiles={attachedFiles}
            onAddFile={onAddFile}
            onRemoveFile={onRemoveFile}
            onOpenVoice={onOpenVoice}
            onOpenEscalation={onOpenEscalation}
            isLoading={isLoading}
            isEmpty={true}
            onSuggestionClick={onSendMessage}
          />
        </div>
      ) : (
        <>
          <div className="flex-1 overflow-y-auto">
            <ChatMessages
              messages={messages}
              isLoading={isLoading}
              onSuggestionClick={onSendMessage}
            />
          </div>
          <div className="border-t bg-background/95 backdrop-blur-sm">
            <ChatInput
              onSendMessage={onSendMessage}
              attachedFiles={attachedFiles}
              onAddFile={onAddFile}
              onRemoveFile={onRemoveFile}
              onOpenVoice={onOpenVoice}
              onOpenEscalation={onOpenEscalation}
              isLoading={isLoading}
              isEmpty={false}
              onSuggestionClick={onSendMessage}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ChatContainer;
