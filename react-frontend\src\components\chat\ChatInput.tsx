import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Mic,
  AlertCircle,
  X,
  FileText,
  Smile
} from 'lucide-react';
import { FileAttachment } from '@/types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn, formatFileSize } from '@/lib/utils';
import { useHotkeys } from 'react-hotkeys-hook';
import SuggestionChips from './SuggestionChips';

interface ChatInputProps {
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  attachedFiles: FileAttachment[];
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  isLoading?: boolean;
  placeholder?: string;
  isEmpty?: boolean; // NEW: true if no messages
  onSuggestionClick?: (query: string) => void; // NEW: for suggestions
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  attachedFiles,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  isLoading = false,
  placeholder = "Ask anything about leaves, benefits, or company policies...",
  isEmpty = false,
  onSuggestionClick,
}) => {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Keyboard shortcuts
  useHotkeys('cmd+enter,ctrl+enter', () => handleSubmit(), {
    enableOnFormTags: ['textarea'],
  });

  useHotkeys('cmd+k,ctrl+k', (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  });

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim(), attachedFiles);
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    files.forEach((file) => {
      const fileAttachment: FileAttachment = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      };
      onAddFile(fileAttachment);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const canSend = message.trim().length > 0 && !isLoading;

  const containerVariants = {
    focused: {
      boxShadow: "0 0 0 2px hsl(var(--ring))",
      transition: { duration: 0.2 }
    },
    unfocused: {
      boxShadow: "0 0 0 0px hsl(var(--ring))",
      transition: { duration: 0.2 }
    }
  };

  return (
    <div className={cn(
      "w-full max-w-2xl mx-auto flex flex-col items-center",
      isEmpty ? "justify-center flex-1 h-full" : "pb-6"
    )}>
      {/* File Attachments */}
      <AnimatePresence>
        {attachedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-3 flex flex-wrap gap-2"
          >
            {attachedFiles.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 rounded-lg bg-muted px-3 py-2 text-sm"
              >
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{file.name}</span>
                <span className="text-muted-foreground">
                  ({formatFileSize(file.size)})
                </span>
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={() => onRemoveFile(file.id)}
                  className="h-5 w-5 hover:bg-destructive hover:text-destructive-foreground"
                >
                  <X className="h-3 w-3" />
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* ChatGPT-style Input Row */}
      <form onSubmit={handleSubmit} className="w-full flex items-center rounded-2xl border border-gray-300 bg-white px-4 py-2 focus-within:ring-2 focus-within:ring-blue-500">
        {/* Main Input Area */}
        <textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={isLoading}
          rows={1}
          className="flex-1 resize-none border-none bg-transparent focus:ring-0 text-base p-0 m-0 outline-none min-h-[44px]"
          style={{ boxShadow: 'none' }}
        />
        {/* Inline Action Buttons */}
        <button
          type="button"
          className="mx-1 text-gray-500 hover:text-black transition"
          disabled={isLoading}
          tabIndex={-1}
        >
          <Smile className="h-5 w-5" />
        </button>
        <button
          type="button"
          className="mx-1 text-gray-500 hover:text-black transition"
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
          tabIndex={-1}
        >
          <Paperclip className="h-5 w-5" />
        </button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileUpload}
        />
        <button
          type="button"
          className="mx-1 text-gray-500 hover:text-black transition"
          onClick={onOpenVoice}
          disabled={isLoading}
          tabIndex={-1}
        >
          <Mic className="h-5 w-5" />
        </button>
        <button
          type="button"
          className="mx-1 text-gray-500 hover:text-black transition"
          onClick={onOpenEscalation}
          disabled={isLoading}
          tabIndex={-1}
        >
          <AlertCircle className="h-5 w-5" />
        </button>
        {/* Send Button */}
        <button
          type="submit"
          className="ml-2 bg-gray-100 hover:bg-blue-600 hover:text-white rounded-full p-2 transition flex items-center justify-center"
          disabled={!canSend}
        >
          <Send className="h-5 w-5" />
        </button>
      </form>
      {/* Suggestion Chips below input */}
      {isEmpty && onSuggestionClick && (
        <div className="w-full flex flex-col items-center mt-6">
          <SuggestionChips onSuggestionClick={onSuggestionClick} />
        </div>
      )}
    </div>
  );
};

export default ChatInput;
